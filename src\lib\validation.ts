/**
 * Validation utilities for form inputs
 * Following the existing validation patterns in the codebase
 */

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validates Nigerian phone numbers
 * Accepts formats: +234XXXXXXXXXX, 234XXXXXXXXXX, 0XXXXXXXXXX, XXXXXXXXXXX
 * Where X represents digits and the total should be 10-11 digits after country code
 */
export const validatePhoneNumber = (phone: string): ValidationResult => {
  if (!phone || phone.trim() === '') {
    return { isValid: false, error: 'Phone number is required' };
  }

  // Remove all spaces, dashes, and parentheses
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');

  // Check for valid Nigerian phone number patterns
  const patterns = [
    /^\+234[789][01]\d{8}$/, // +234XXXXXXXXXX (11 digits after +234)
    /^234[789][01]\d{8}$/,   // 234XXXXXXXXXX (11 digits after 234)
    /^0[789][01]\d{8}$/,     // 0XXXXXXXXXX (11 digits starting with 0)
    /^[789][01]\d{8}$/       // XXXXXXXXXX (10 digits starting with 7, 8, or 9)
  ];

  const isValidFormat = patterns.some(pattern => pattern.test(cleanPhone));

  if (!isValidFormat) {
    return {
      isValid: false,
      error: 'Please enter a valid Nigerian phone number (e.g., +2348012345678, 08012345678)'
    };
  }

  return { isValid: true };
};

/**
 * Validates email addresses using a comprehensive regex pattern
 */
export const validateEmail = (email: string): ValidationResult => {
  if (!email || email.trim() === '') {
    return { isValid: false, error: 'Email address is required' };
  }

  // Comprehensive email validation regex
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  if (!emailRegex.test(email.trim())) {
    return {
      isValid: false,
      error: 'Please enter a valid email address (e.g., <EMAIL>)'
    };
  }

  // Additional check for minimum domain length
  const domain = email.split('@')[1];
  if (domain && domain.length < 3) {
    return {
      isValid: false,
      error: 'Please enter a valid email address with a proper domain'
    };
  }

  return { isValid: true };
};

/**
 * Validates guarantor name (non-empty, reasonable length)
 */
export const validateGuarantorName = (name: string): ValidationResult => {
  if (!name || name.trim() === '') {
    return { isValid: false, error: 'Guarantor name is required' };
  }

  const trimmedName = name.trim();
  
  if (trimmedName.length < 2) {
    return {
      isValid: false,
      error: 'Guarantor name must be at least 2 characters long'
    };
  }

  if (trimmedName.length > 100) {
    return {
      isValid: false,
      error: 'Guarantor name must not exceed 100 characters'
    };
  }

  // Check for valid name characters (letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-Z\s\-'\.]+$/;
  if (!nameRegex.test(trimmedName)) {
    return {
      isValid: false,
      error: 'Guarantor name can only contain letters, spaces, hyphens, and apostrophes'
    };
  }

  return { isValid: true };
};

/**
 * Validates guarantor relationship
 */
export const validateGuarantorRelationship = (relationship: string): ValidationResult => {
  if (!relationship || relationship.trim() === '') {
    return { isValid: false, error: 'Relationship is required' };
  }

  const trimmedRelationship = relationship.trim();
  
  if (trimmedRelationship.length < 2) {
    return {
      isValid: false,
      error: 'Relationship must be at least 2 characters long'
    };
  }

  if (trimmedRelationship.length > 50) {
    return {
      isValid: false,
      error: 'Relationship must not exceed 50 characters'
    };
  }

  return { isValid: true };
};

/**
 * Validates all guarantor fields at once
 */
export const validateGuarantorInfo = (guarantorData: {
  name: string;
  phone: string;
  email: string;
  relationship: string;
}): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  const nameValidation = validateGuarantorName(guarantorData.name);
  if (!nameValidation.isValid) {
    errors.name = nameValidation.error!;
  }

  const phoneValidation = validatePhoneNumber(guarantorData.phone);
  if (!phoneValidation.isValid) {
    errors.phone = phoneValidation.error!;
  }

  const emailValidation = validateEmail(guarantorData.email);
  if (!emailValidation.isValid) {
    errors.email = emailValidation.error!;
  }

  const relationshipValidation = validateGuarantorRelationship(guarantorData.relationship);
  if (!relationshipValidation.isValid) {
    errors.relationship = relationshipValidation.error!;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Formats phone number for display (adds country code if missing)
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  
  // If it starts with 0, replace with +234
  if (cleanPhone.startsWith('0')) {
    return `+234${cleanPhone.substring(1)}`;
  }
  
  // If it starts with 234, add +
  if (cleanPhone.startsWith('234')) {
    return `+${cleanPhone}`;
  }
  
  // If it's 10 digits starting with 7, 8, or 9, add +234
  if (/^[789]\d{9}$/.test(cleanPhone)) {
    return `+234${cleanPhone}`;
  }
  
  return phone; // Return as-is if already formatted or invalid
};

/**
 * Real-time validation for input fields
 * Returns validation state and error message for UI feedback
 */
export const getFieldValidationState = (
  fieldType: 'phone' | 'email' | 'name' | 'relationship',
  value: string,
  isRequired: boolean = true
): { isValid: boolean; error: string; showError: boolean } => {
  // Don't show errors for empty fields unless they're required and user has interacted
  if (!value.trim() && !isRequired) {
    return { isValid: true, error: '', showError: false };
  }

  let validation: ValidationResult;
  
  switch (fieldType) {
    case 'phone':
      validation = validatePhoneNumber(value);
      break;
    case 'email':
      validation = validateEmail(value);
      break;
    case 'name':
      validation = validateGuarantorName(value);
      break;
    case 'relationship':
      validation = validateGuarantorRelationship(value);
      break;
    default:
      validation = { isValid: true };
  }

  return {
    isValid: validation.isValid,
    error: validation.error || '',
    showError: !validation.isValid && value.trim() !== ''
  };
};
