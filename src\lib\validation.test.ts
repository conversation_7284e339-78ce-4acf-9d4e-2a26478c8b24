/**
 * Test file for validation utilities
 * Run this with: node src/lib/validation.test.js (after compiling)
 * Or just review the test cases manually
 */

// Since we can't easily run TypeScript tests in this environment,
// let's create a comprehensive test plan that can be manually verified

console.log('📋 Validation Test Plan for Guarantor Fields Bug #970\n');

// Test results tracking
let totalTests = 0;
let passedTests = 0;

function test(description: string, testFn: () => boolean) {
  totalTests++;
  try {
    const result = testFn();
    if (result) {
      passedTests++;
      console.log(`✅ ${description}`);
    } else {
      console.log(`❌ ${description}`);
    }
  } catch (error) {
    console.log(`❌ ${description} - Error: ${error}`);
  }
}

function assertEquals(actual: any, expected: any): boolean {
  return JSON.stringify(actual) === JSON.stringify(expected);
}

console.log('🧪 Running Validation Tests...\n');

// Phone Number Validation Tests
console.log('📱 Phone Number Validation Tests:');

test('Valid Nigerian phone number with +234', () => {
  const result = validatePhoneNumber('+2348012345678');
  return result.isValid === true;
});

test('Valid Nigerian phone number with 234', () => {
  const result = validatePhoneNumber('2348012345678');
  return result.isValid === true;
});

test('Valid Nigerian phone number with 0', () => {
  const result = validatePhoneNumber('08012345678');
  return result.isValid === true;
});

test('Valid Nigerian phone number without prefix', () => {
  const result = validatePhoneNumber('8012345678');
  return result.isValid === true;
});

test('Invalid phone number - too short', () => {
  const result = validatePhoneNumber('801234567');
  return result.isValid === false && result.error?.includes('valid Nigerian phone number');
});

test('Invalid phone number - letters', () => {
  const result = validatePhoneNumber('abc123def');
  return result.isValid === false && result.error?.includes('valid Nigerian phone number');
});

test('Empty phone number', () => {
  const result = validatePhoneNumber('');
  return result.isValid === false && result.error?.includes('required');
});

test('Phone number with spaces and dashes', () => {
  const result = validatePhoneNumber('+234 ************');
  return result.isValid === true;
});

// Email Validation Tests
console.log('\n📧 Email Validation Tests:');

test('Valid email address', () => {
  const result = validateEmail('<EMAIL>');
  return result.isValid === true;
});

test('Valid email with subdomain', () => {
  const result = validateEmail('<EMAIL>');
  return result.isValid === true;
});

test('Invalid email - missing @', () => {
  const result = validateEmail('userexample.com');
  return result.isValid === false && result.error?.includes('valid email address');
});

test('Invalid email - missing domain', () => {
  const result = validateEmail('user@');
  return result.isValid === false && result.error?.includes('valid email address');
});

test('Empty email', () => {
  const result = validateEmail('');
  return result.isValid === false && result.error?.includes('required');
});

test('Invalid email - short domain', () => {
  const result = validateEmail('user@a.b');
  return result.isValid === false && result.error?.includes('proper domain');
});

// Guarantor Name Validation Tests
console.log('\n👤 Guarantor Name Validation Tests:');

test('Valid guarantor name', () => {
  const result = validateGuarantorName('John Doe');
  return result.isValid === true;
});

test('Valid guarantor name with apostrophe', () => {
  const result = validateGuarantorName("O'Connor");
  return result.isValid === true;
});

test('Valid guarantor name with hyphen', () => {
  const result = validateGuarantorName('Mary-Jane Smith');
  return result.isValid === true;
});

test('Invalid guarantor name - too short', () => {
  const result = validateGuarantorName('A');
  return result.isValid === false && result.error?.includes('at least 2 characters');
});

test('Invalid guarantor name - numbers', () => {
  const result = validateGuarantorName('John123');
  return result.isValid === false && result.error?.includes('letters, spaces, hyphens');
});

test('Empty guarantor name', () => {
  const result = validateGuarantorName('');
  return result.isValid === false && result.error?.includes('required');
});

// Guarantor Relationship Validation Tests
console.log('\n🤝 Guarantor Relationship Validation Tests:');

test('Valid relationship', () => {
  const result = validateGuarantorRelationship('Spouse');
  return result.isValid === true;
});

test('Invalid relationship - too short', () => {
  const result = validateGuarantorRelationship('A');
  return result.isValid === false && result.error?.includes('at least 2 characters');
});

test('Empty relationship', () => {
  const result = validateGuarantorRelationship('');
  return result.isValid === false && result.error?.includes('required');
});

// Phone Number Formatting Tests
console.log('\n📱 Phone Number Formatting Tests:');

test('Format phone starting with 0', () => {
  const result = formatPhoneNumber('08012345678');
  return result === '+2348012345678';
});

test('Format phone starting with 234', () => {
  const result = formatPhoneNumber('2348012345678');
  return result === '+2348012345678';
});

test('Format 10-digit phone', () => {
  const result = formatPhoneNumber('8012345678');
  return result === '+2348012345678';
});

test('Already formatted phone', () => {
  const result = formatPhoneNumber('+2348012345678');
  return result === '+2348012345678';
});

// Complete Guarantor Info Validation Tests
console.log('\n🔍 Complete Guarantor Info Validation Tests:');

test('Valid guarantor info', () => {
  const result = validateGuarantorInfo({
    name: 'John Doe',
    phone: '+2348012345678',
    email: '<EMAIL>',
    relationship: 'Spouse'
  });
  return result.isValid === true && Object.keys(result.errors).length === 0;
});

test('Invalid guarantor info - multiple errors', () => {
  const result = validateGuarantorInfo({
    name: '',
    phone: 'invalid',
    email: 'invalid-email',
    relationship: ''
  });
  return result.isValid === false && Object.keys(result.errors).length === 4;
});

// Field Validation State Tests
console.log('\n🎯 Field Validation State Tests:');

test('Field validation state - valid phone', () => {
  const result = getFieldValidationState('phone', '+2348012345678', true);
  return result.isValid === true && result.showError === false;
});

test('Field validation state - invalid phone', () => {
  const result = getFieldValidationState('phone', 'invalid', true);
  return result.isValid === false && result.showError === true;
});

test('Field validation state - empty optional field', () => {
  const result = getFieldValidationState('phone', '', false);
  return result.isValid === true && result.showError === false;
});

// Summary
console.log('\n📊 Test Results Summary:');
console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${totalTests - passedTests}`);
console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 All tests passed! Validation implementation is working correctly.');
} else {
  console.log('\n⚠️  Some tests failed. Please review the validation implementation.');
}

export {};
